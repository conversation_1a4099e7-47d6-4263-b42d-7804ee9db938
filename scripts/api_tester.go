package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strings"
	"time"
)

// 请求结构体
type ImageRequest struct {
	ImageURL string `json:"image_url"`
}

// 响应结构体
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// 测试结果统计
type TestStats struct {
	TotalRequests    int
	SuccessRequests  int
	FailedRequests   int
	TimeoutRequests  int
	StartTime        time.Time
	EndTime          time.Time
	ResponseTimes    []time.Duration
	Errors           []string
}

// 测试配置
type TestConfig struct {
	BaseURL      string
	ImageBaseURL string
	StartImage   int
	EndImage     int
	Interval     time.Duration
	Timeout      time.Duration
}

func main() {
	// 配置参数
	config := TestConfig{
		BaseURL:      "http://localhost:8080",  // 默认本地地址，可通过环境变量修改
		ImageBaseURL: "http://solve.igmdns.com/img/",
		StartImage:   1,
		EndImage:     200,
		Interval:     15 * time.Second,
		Timeout:      30 * time.Second,
	}

	// 从环境变量读取API地址
	if apiURL := os.Getenv("API_URL"); apiURL != "" {
		config.BaseURL = apiURL
	}

	// 初始化统计
	stats := &TestStats{
		StartTime:     time.Now(),
		ResponseTimes: make([]time.Duration, 0),
		Errors:        make([]string, 0),
	}

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: config.Timeout,
	}

	fmt.Printf("🚀 开始API测试\n")
	fmt.Printf("📍 API地址: %s/api/v1/process-image\n", config.BaseURL)
	fmt.Printf("🖼️  图片范围: %02d.jpg - %02d.jpg\n", config.StartImage, config.EndImage)
	fmt.Printf("⏱️  请求间隔: %v\n", config.Interval)
	fmt.Printf("⏰ 超时时间: %v\n", config.Timeout)
	fmt.Printf("📊 总请求数: %d\n", config.EndImage-config.StartImage+1)
	fmt.Printf("🕐 预计耗时: %v\n", time.Duration(config.EndImage-config.StartImage)*config.Interval)
	fmt.Println(strings.Repeat("=", 60))

	// 开始测试
	for i := config.StartImage; i <= config.EndImage; i++ {
		imageURL := fmt.Sprintf("%s%02d.jpg", config.ImageBaseURL, i)
		
		fmt.Printf("\n[%d/%d] 测试图片: %s\n", i, config.EndImage, imageURL)
		
		// 发送请求
		success, responseTime, err := sendRequest(client, config.BaseURL, imageURL)
		
		// 更新统计
		stats.TotalRequests++
		stats.ResponseTimes = append(stats.ResponseTimes, responseTime)
		
		if success {
			stats.SuccessRequests++
			fmt.Printf("✅ 成功 - 响应时间: %v\n", responseTime)
		} else {
			stats.FailedRequests++
			errorMsg := fmt.Sprintf("图片 %02d.jpg: %v", i, err)
			stats.Errors = append(stats.Errors, errorMsg)
			
			if err != nil && err.Error() == "timeout" {
				stats.TimeoutRequests++
				fmt.Printf("⏰ 超时 - 响应时间: %v\n", responseTime)
			} else {
				fmt.Printf("❌ 失败 - 错误: %v\n", err)
			}
		}
		
		// 显示当前统计
		fmt.Printf("📊 当前统计: 成功 %d, 失败 %d, 超时 %d\n", 
			stats.SuccessRequests, stats.FailedRequests, stats.TimeoutRequests)
		
		// 如果不是最后一个请求，等待间隔时间
		if i < config.EndImage {
			fmt.Printf("⏳ 等待 %v 后继续...\n", config.Interval)
			time.Sleep(config.Interval)
		}
	}

	// 测试完成，生成报告
	stats.EndTime = time.Now()
	generateReport(stats, config)
}

// 发送API请求
func sendRequest(client *http.Client, baseURL, imageURL string) (bool, time.Duration, error) {
	// 准备请求数据
	requestData := ImageRequest{
		ImageURL: imageURL,
	}
	
	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return false, 0, fmt.Errorf("JSON编码失败: %v", err)
	}
	
	// 创建请求
	url := fmt.Sprintf("%s/api/v1/process-image", baseURL)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return false, 0, fmt.Errorf("创建请求失败: %v", err)
	}
	
	req.Header.Set("Content-Type", "application/json")
	
	// 发送请求并计时
	startTime := time.Now()
	resp, err := client.Do(req)
	responseTime := time.Since(startTime)
	
	if err != nil {
		if os.IsTimeout(err) {
			return false, responseTime, fmt.Errorf("timeout")
		}
		return false, responseTime, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()
	
	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, responseTime, fmt.Errorf("读取响应失败: %v", err)
	}
	
	// 解析响应
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return false, responseTime, fmt.Errorf("解析响应失败: %v", err)
	}
	
	// 检查响应状态
	if resp.StatusCode != 200 || apiResp.Code != 200 {
		return false, responseTime, fmt.Errorf("API返回错误: HTTP %d, Code %d, Message: %s", 
			resp.StatusCode, apiResp.Code, apiResp.Message)
	}
	
	return true, responseTime, nil
}

// 生成测试报告
func generateReport(stats *TestStats, config TestConfig) {
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("📋 测试报告")
	fmt.Println(strings.Repeat("=", 60))
	
	// 基本统计
	fmt.Printf("🕐 测试时间: %v - %v\n", stats.StartTime.Format("2006-01-02 15:04:05"), stats.EndTime.Format("2006-01-02 15:04:05"))
	fmt.Printf("⏱️  总耗时: %v\n", stats.EndTime.Sub(stats.StartTime))
	fmt.Printf("📊 总请求数: %d\n", stats.TotalRequests)
	fmt.Printf("✅ 成功请求: %d (%.2f%%)\n", stats.SuccessRequests, float64(stats.SuccessRequests)/float64(stats.TotalRequests)*100)
	fmt.Printf("❌ 失败请求: %d (%.2f%%)\n", stats.FailedRequests, float64(stats.FailedRequests)/float64(stats.TotalRequests)*100)
	fmt.Printf("⏰ 超时请求: %d (%.2f%%)\n", stats.TimeoutRequests, float64(stats.TimeoutRequests)/float64(stats.TotalRequests)*100)
	
	// 响应时间统计
	if len(stats.ResponseTimes) > 0 {
		var totalTime time.Duration
		var minTime, maxTime time.Duration
		minTime = stats.ResponseTimes[0]
		maxTime = stats.ResponseTimes[0]
		
		for _, t := range stats.ResponseTimes {
			totalTime += t
			if t < minTime {
				minTime = t
			}
			if t > maxTime {
				maxTime = t
			}
		}
		
		avgTime := totalTime / time.Duration(len(stats.ResponseTimes))
		
		fmt.Printf("\n📈 响应时间统计:\n")
		fmt.Printf("   平均响应时间: %v\n", avgTime)
		fmt.Printf("   最快响应时间: %v\n", minTime)
		fmt.Printf("   最慢响应时间: %v\n", maxTime)
	}
	
	// 错误详情
	if len(stats.Errors) > 0 {
		fmt.Printf("\n❌ 错误详情:\n")
		for i, err := range stats.Errors {
			if i < 10 { // 只显示前10个错误
				fmt.Printf("   %d. %s\n", i+1, err)
			}
		}
		if len(stats.Errors) > 10 {
			fmt.Printf("   ... 还有 %d 个错误\n", len(stats.Errors)-10)
		}
	}
	
	// 保存详细报告到文件
	saveDetailedReport(stats, config)
	
	fmt.Println(strings.Repeat("=", 60))
	fmt.Println("🎉 测试完成!")
}

// 保存详细报告到文件
func saveDetailedReport(stats *TestStats, config TestConfig) {
	timestamp := time.Now().Format("20060102_150405")
	filename := fmt.Sprintf("test_results_%s/api_test_report.json", timestamp)
	
	// 创建目录
	os.MkdirAll(fmt.Sprintf("test_results_%s", timestamp), 0755)
	
	// 准备报告数据
	report := map[string]interface{}{
		"test_config": config,
		"test_stats": stats,
		"summary": map[string]interface{}{
			"total_requests":   stats.TotalRequests,
			"success_requests": stats.SuccessRequests,
			"failed_requests":  stats.FailedRequests,
			"timeout_requests": stats.TimeoutRequests,
			"success_rate":     float64(stats.SuccessRequests) / float64(stats.TotalRequests) * 100,
			"test_duration":    stats.EndTime.Sub(stats.StartTime).String(),
		},
	}
	
	// 保存到文件
	jsonData, _ := json.MarshalIndent(report, "", "  ")
	if err := os.WriteFile(filename, jsonData, 0644); err != nil {
		log.Printf("保存报告失败: %v", err)
	} else {
		fmt.Printf("📄 详细报告已保存到: %s\n", filename)
	}
}
