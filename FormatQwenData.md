这里是对FormatQwenData方法的逻辑进行描述：

qwen相应的数据中的content字段内包含以下内容

{
  "question_type": "多选题",
  "question_text": "雾天跟车行驶,应如何安全驾驶?",
  "A": "加大跟车距离,降低行驶速度",
  "B": "提前开启雾灯、危险报警闪光灯",
  "C": "以前车尾灯作为判断安全距离的参照物",
  "D": "按喇叭提示行车位置"
}

question_type为题目类型
question_text为题目内容
ABCD对应四个选项

另外当question_type为判断题是选项只有两个

{
  "question_type": "多选题",
  "question_text": "雾天跟车行驶,应如何安全驾驶?",
    "Y": "正确",
    "N": "错误"
}

Y与N的位置可能颠倒，ABCD的位置不会发生颠倒。

这是预期内的格式；
非预期的问题为；

question_type =  若为空或出现了判断题/多选题/单选题以外的值则中止进程给用户返回“图片解析异常，请重新拍摄”


question_text = 存在一个复杂的非预期的问题如下；

在题干的开始，可能包含以下几种情况：
（**题）01、
(**题)01.
01、
01.

(判断题)08、等17分钟后如果没问题就可以走了。
(判断题)08.等17分钟后如果没问题就可以走了。
08.等17分钟后如果没问题就可以走了。
08、等17分钟后如果没问题就可以走了。
断题)08、等17分钟后如果没问题就可以走了。
判断题08、等17分钟后如果没问题就可以走了。
题)08、等17分钟后如果没问题就可以走了。



我这里准备了一个正则用来清洗题干。应该够用了
^[(（【]?(判断题|单选题|多选题|断题|题)?[)）】]?\s*0?(?:[1-9]|1[0-9]|20)[、.．]?\s*

## 优化记录

### 2024年优化：换行符清洗
在FormatQwenData方法中增加了全文换行符清洗步骤：

1. **处理流程优化**：
   - 第一步：对所有字段进行换行符清洗（新增）
   - 第二步：对题干执行原有的格式清洗逻辑

2. **换行符清洗范围**：
   - `question_text`：题干内容
   - `A`、`B`、`C`、`D`：选择题选项
   - `Y`、`N`：判断题选项
   - 所有字段都会被清洗换行符

3. **换行符清洗逻辑**：
   - 清洗各种换行符：`\r\n`、`\n`、`\r`
   - **直接移除换行符**，不使用空格替换
   - 保持文本内容的连续性

4. **实现函数**：
   - 新增`cleanLineBreaks()`函数专门处理换行符
   - 保持原有`CleanQuestionText()`逻辑不变
   - 确保两步清洗的顺序：全文换行符清洗 → 题干格式清洗

5. **技术实现**：
   - 在JSON解析后对各字段值进行换行符清理
   - 使用正则表达式`\r\n|\r|\n`匹配所有换行符类型
   - 直接替换为空字符串，实现完全移除
